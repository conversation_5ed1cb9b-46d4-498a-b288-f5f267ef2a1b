import type { Logger } from "pino";
import type { Request, Response, NextFunction } from "express";
import type { RateLimitRequestHandler } from "express-rate-limit";
import type { Config } from "../config";
import type { ExtractService } from "../extract";
import type { AuthService } from "../auth";
import type { UserService } from "../user";
import type { RequestService } from "../request";

import { z } from "zod";
import cors from "cors";
import helmet from "helmet";
import express from "express";
import session from "express-session";
import FileStore from "session-file-store";
import cookieParser from "cookie-parser";
import errorHandler from "strong-error-handler";
import { rateLimit } from "express-rate-limit";
import audit from "express-requests-logger";
import * as Api from "../shared/api";
import { bodyValidator, paramsValidator, queryValidator } from "../middleware";
import { Injectable, trycatchAsync } from "../utils";
import { createSignalError<PERSON>andler } from "../error/http";
import { AlreadyExistsSignalError, InternalServerErrorSignalError, NotFoundSignalError } from "../error/http";
import { SignalError } from "../error/signal";

// Extend Express Request type to include session user
declare module "express-session" {
    interface SessionData {
        userId: string;
    }
}

const S_PER_DAY = 24 * 60 * 60;
const MS_PER_DAY = S_PER_DAY * 1000;

/**
 * @param limit - requests per window, default 60
 * @param window - minutes, default 60
 */
function createRateLimit(limit = 60, window = 60): RateLimitRequestHandler {
    return rateLimit({
        windowMs: window * 60 * 1000,
        limit,
        standardHeaders: "draft-8",
        legacyHeaders: false,
    });
}

export class Server extends Injectable {
    private readonly isDev!: boolean;

    private readonly logger!: Logger;
    private readonly config!: Config;
    private readonly extractService!: ExtractService;
    private readonly authService!: AuthService;
    private readonly userService!: UserService;
    private readonly requestService!: RequestService;

    run() {
        const app = express();

        app.use(
            errorHandler({
                debug: this.isDev,
                log: true,
            }),
        );

        // Basic middleware
        app.use(
            cors(),
            helmet(),
            cookieParser(),
            express.json(),
            express.urlencoded({ extended: true }),
            audit({
                logger: this.logger,
            }),
        );

        const sessionExpireTimeDays = this.config.getWithValidation(
            "SESSION_EXPIRE_TIME_DAYS",
            z.coerce.number().int().positive(),
        );

        // Configure session middleware
        const FileStoreSession = FileStore(session);
        app.use(session({
            secret: this.config.get<string>("SESSION_SECRET"),
            resave: false,
            saveUninitialized: false,
            name: "session",
            cookie: {
                secure: false, // !this.isDev, // Use secure cookies in production
                httpOnly: true,
                maxAge: sessionExpireTimeDays * MS_PER_DAY,
            },
            store: new FileStoreSession({
                path: "./.sessions",
                ttl: sessionExpireTimeDays * S_PER_DAY,
            }),
        }));

        // Authentication middleware
        const requireAuth = (req: Request, res: Response, next: NextFunction) => {
            if (!req.session.userId) {
                res.sendStatus(401);

                return;
            }

            next();
        };

        app.post(
            "/auth/otp",
            createRateLimit(),
            bodyValidator(Api.OtpRequest),
            async (req, res) => {
                const data = req.body as Api.OtpRequest;

                await this.authService.otp(data);

                res.sendStatus(200);
            },
        );

        app.post(
            "/auth/sign-up",
            createRateLimit(),
            bodyValidator(Api.SignUpRequest),
            async (req, res) => {
                const data = req.body as Api.SignUpRequest;

                await trycatchAsync(
                    async () => {
                        const user = await this.authService.signUp(data);

                        // Regenerate session
                        req.session.regenerate((err) => {
                            if (err) {
                                this.logger.error("Session regeneration error:");
                                this.logger.error(err);

                                throw new InternalServerErrorSignalError("session regeneration");
                            }

                            // Store user ID in session
                            req.session.userId = user.id;

                            // Save session before responding
                            req.session.save((err) => {
                                if (err) {
                                    this.logger.error("Session save error:");
                                    this.logger.error(err);

                                    throw new InternalServerErrorSignalError("session save");
                                }

                                res.sendStatus(201);
                            });
                        });
                    },
                    [
                        [SignalError, (e) => { console.log("signal error", e); throw e }],
                    ],
                    (e) => {
                        console.log("catch-all", e);
                        if (e instanceof Error && e.message.includes("nique constraint")) {
                            throw new AlreadyExistsSignalError("user");
                        }

                        this.logger.error("Sign-up error:");
                        this.logger.error(e);

                        throw new InternalServerErrorSignalError("sign up");
                    },
                );
            },
        );

        // Authentication routes
        app.post(
            "/auth/sign-in",
            createRateLimit(),
            bodyValidator(Api.SignInRequest),
            async (req, res) => {
                const data = req.body as Api.SignInRequest;

                await trycatchAsync(
                    async () => {
                        const user = await this.authService.signIn(data);

                        // Regenerate session to prevent session fixation
                        req.session.regenerate((err) => {
                            if (err) {
                                this.logger.error("Session regeneration error:");
                                this.logger.error(err);

                                throw new InternalServerErrorSignalError("session regeneration");
                            }

                            // Store user ID in session
                            req.session.userId = user.id;

                            // Save session before responding
                            req.session.save((err) => {
                                if (err) {
                                    this.logger.error("Session save error:");
                                    this.logger.error(err);

                                    throw new InternalServerErrorSignalError("session save");
                                }

                                res.sendStatus(200);
                            });
                        });
                    },
                    [
                        [SignalError, (e) => { throw e }],
                    ],
                    (e) => {
                        this.logger.error("Authentication error:");
                        this.logger.error(e);

                        throw new InternalServerErrorSignalError("authentication", e);
                    },
                );
            },
        );

        app.post(
            "/auth/sign-out",
            createRateLimit(),
            (req, res) => {
                // Destroy the session
                req.session.destroy((err) => {
                    if (err) {
                        this.logger.error("Session destruction error:");
                        this.logger.error(err);

                        throw new InternalServerErrorSignalError("session destruction");
                    }

                    // Clear the cookie
                    res.clearCookie("session");
                    res.sendStatus(200);
                });
            });

        app.post(
            "/auth/reset-password",
            createRateLimit(),
            bodyValidator(Api.ResetPasswordRequest),
            async (req, res) => {
                const data = req.body as Api.ResetPasswordRequest;

                await this.authService.resetPassword(data);

                res.sendStatus(200);
            },
        );

        app.get("/auth/me", requireAuth, async (req, res) => {
            const user = await this.userService.getById(req.session.userId!);

            if (!user) {
                throw new NotFoundSignalError("user");
            }

            res.json({
                id: user.id,
                email: user.email,
            });
        });

        // Extract route
        app.post(
            "/extract",
            createRateLimit(),
            queryValidator(Api.ExtractRequestQuery),
            bodyValidator(Api.ExtractRequest),
            async (req, res) => {
                const { type } = (req as unknown as { queryParams: Api.ExtractRequestQuery }).queryParams;
                const { text } = req.body as Api.ExtractRequest;

                switch (type) {
                    case "v1": {
                        const result = await this.extractService.extractV1({
                            text,
                        });

                        res.json(result);

                        break;
                    }

                    case "v2":
                    default: {
                        const result = await this.extractService.extractV2({
                            text,
                        });

                        const rawResponse = {
                            service_type: result.metadata.serviceType,
                            incoterms: result.metadata.incoterms?.join(", "),
                            origin: result.metadata.origin.country,
                            destination_country: result.metadata.destination.country,

                            pickup: {
                                city: result.metadata.origin.city,
                                zip_code: result.metadata.origin.zipCode,
                                address: result.metadata.origin.address,
                                is_needed: false,
                            },

                            delivery: {
                                city: result.metadata.destination.city,
                                zip_code: result.metadata.destination.zipCode,
                                address: result.metadata.destination.address,
                                is_needed: false,
                            },

                            summary: {
                                piece: result.digits.totalUnits,
                                weight: result.digits.totalWeight,
                                volume: result.digits.totalVolume,
                                density: null,
                                chargeable_weight: null,
                            },

                            details: result.digits.packages?.map((item) => ({
                                piece: item.units,
                                dimension: {
                                    width: item.width ?? undefined,
                                    height: item.height ?? undefined,
                                    length: item.length ?? undefined,
                                    volume: undefined,
                                    weight: item.weight ?? undefined,
                                    is_stackable: item.isStackable,
                                },
                            })),

                            additional_details: {
                                description_of_goods: result.services.descriptionOfGoods,
                                hs_codes: result.services.hsCodes?.[0] ?? null,
                                costs_of_goods: result.services.totalCostOfGoods,
                                services: null,
                                selected_services: result.services.additionalServices,
                            },
                        } satisfies z.input<typeof Api.ExtractResponse>;

                        const response = Api.ExtractResponse.safeParse(rawResponse);

                        if (response.success) {
                            res.json(response.data);
                        }
                        else {
                            throw new InternalServerErrorSignalError("transforming response");
                        }
                    }
                }
            },
        );

        app.post(
            "/request",
            createRateLimit(),
            requireAuth,
            bodyValidator(Api.CreateRequestRequest),
            async (req, res) => {
                const body = req.body as Api.CreateRequestRequest;

                const { id } = await this.requestService.create({
                    userId: req.session.userId!,

                    origin: body.origin,
                    destination: body.destination,

                    service: body.service,

                    quantity: body.quantity,
                    weight: body.weight,
                    volume: body.volume,
                    chargeableWeight: body.chargeableWeight,
                });

                res.status(201).json({
                    id,
                });
            },
        );

        app.post(
            "/request/:id/status/:status",
            createRateLimit(),
            requireAuth,
            paramsValidator(Api.UpdateRequestStatusRequest),
            async (req, res) => {
                const { id, status } = req.params as Api.UpdateRequestStatusRequest;

                await this.requestService.updateStatus({
                    userId: req.session.userId!,

                    id,
                    status,
                });

                res.sendStatus(200);
            },
        );

        app.get(
            "/request",
            createRateLimit(),
            requireAuth,
            queryValidator(Api.GetRequestsRequest),
            async (req, res) => {
                const data = (req as unknown as { queryParams: Api.GetRequestsRequest }).queryParams;

                console.dir({ data }, { depth: null });

                const requests = await this.requestService.getMany({
                    userId: req.session.userId!,

                    origin: data.origin,
                    destination: data.destination,

                    service: data.service,

                    from: data.from,
                    to: data.to,
                });

                res.json(requests.map(request => ({
                    id: request.id,

                    ordinal: request.ordinal,

                    origin: request.origin,
                    destination: request.destination,

                    service: request.service,

                    quantity: request.quantity,
                    weight: request.weight,
                    volume: request.volume,
                    chargeableWeight: request.chargeableWeight,

                    status: request.status,

                    createdAt: request.createdAt,
                })));
            },
        );

        app.get(
            "/request/locations",
            createRateLimit(),
            requireAuth,
            async (req, res) => {
                const locations = await this.requestService.getAvailableLocations({
                    userId: req.session.userId!,
                });

                res.json(locations);
            },
        );

        app.delete(
            "/request/:id",
            createRateLimit(),
            requireAuth,
            paramsValidator(Api.DeleteRequestRequest),
            async (req, res) => {
                const { id } = req.params as Api.DeleteRequestRequest;

                await this.requestService.softDelete({
                    id,
                    userId: req.session.userId!,
                });

                res.sendStatus(200);
            },
        );

        app.use(createSignalErrorHandler());

        const port = this.config.getWithValidation(
            "SERVER_PORT",
            z.coerce.number().int().positive(),
        );

        app.listen(port, () => {
            this.logger.info(`Server is running on port ${port}`);
        });
    }
}
